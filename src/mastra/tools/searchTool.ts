import { createTool } from "@mastra/core/tools";
import { z } from "zod";

const MultiSearchQuerySchema = z.object({
  q: z.string(),
  filters: z.union([z.array(z.string()), z.string()]).optional(),
  sort: z.union([z.array(z.string()), z.string()]).optional(),
});

// const MultiSearchQuerySchema = z.object({
//   q: z.string(),

//   // Фильтрация
//   filters: z.union([z.array(z.string()), z.string()]).optional(),

//   // Сортировка
//   sort: z.union([z.array(z.string()), z.string()]).optional(),

//   // Пагинация
//   limit: z.number().min(1).max(1000).default(20).optional(),
//   offset: z.number().min(0).default(0).optional(),

//   // Какие поля возвращать
//   attributesToRetrieve: z.array(z.string()).optional(),

//   // Какие поля подсвечивать в результатах
//   attributesToHighlight: z.array(z.string()).optional(),

//   // Какие поля обрезать в результатах
//   attributesToCrop: z.array(z.string()).optional(),
//   cropLength: z.number().min(1).max(1000).default(200).optional(),
//   cropMarker: z.string().default("...").optional(),

//   // Подсветка найденных слов
//   highlightPreTag: z.string().default("<mark>").optional(),
//   highlightPostTag: z.string().default("</mark>").optional(),

//   // Показывать ли количество совпадений
//   showMatchesPosition: z.boolean().default(false).optional(),

//   // Показывать ли детали ранжирования
//   showRankingScore: z.boolean().default(false).optional(),
//   showRankingScoreDetails: z.boolean().default(false).optional(),

//   // Поиск по конкретным атрибутам
//   attributesToSearchOn: z.array(z.string()).optional(),

//   // Стратегия поиска
//   matchingStrategy: z.enum(["last", "all", ""]).default("last").optional(),

//   // Настройки фасетов (для группировки результатов)
//   // facets: z.array(z.string()).optional(),

//   // Дополнительные настройки
//   distinct: z.string().optional(), // поле для дедупликации
//   // Локализация
// });

export const MultiSearchSchema = z.object({
  queries: z.array(MultiSearchQuerySchema),
});

export type MultiSearchQueries = z.infer<typeof MultiSearchSchema>;

// Zod схема для Federation
const FederationSchema = z.object({
  indexUid: z.string(),
  queriesPosition: z.number(),
  weightedRankingScore: z.number(),
});

// Zod схема для Formatted
const FormattedSchema = z.object({
  prod_id: z.string(),
  prod_sku: z.string(),
  prod_analogsku: z.string(),
  prod_analogs: z.string(),
  prod_purpose: z.string(),
  prod_type: z.string(),
  prod_material: z.string(),
  prod_uses: z.string(),
  prod_note: z.string(),
  prod_manuf: z.string(),
  prod_year: z.string(),
  prod_size: z.string(),
  prod_model: z.string(),
  prod_images: z.string(),
  prod_cat: z.string(),
  prod_price: z.string(),
  prod_count: z.string(),
  prod_discount: z.string(),
  prod_group: z.string(),
  prod_group_count: z.string(),
  prod_rk: z.string(),
  prod_img: z.string(),
  prod_img_rumi: z.string(),
});

// Zod схема для Daum
const DaumSchema = z.object({
  prod_id: z.number(),
  prod_sku: z.string(),
  prod_analogsku: z.string(),
  prod_analogs: z.string(),
  prod_purpose: z.string(),
  prod_type: z.string(),
  prod_material: z.string(),
  prod_uses: z.string(),
  prod_note: z.string(),
  prod_manuf: z.string(),
  prod_year: z.string(),
  prod_size: z.string(),
  prod_model: z.string(),
  prod_images: z.string(),
  prod_cat: z.string(),
  prod_price: z.string(),
  prod_count: z.number(),
  prod_discount: z.number(),
  prod_group: z.string(),
  prod_group_count: z.number(),
  prod_rk: z.string(),
  prod_img: z.string(),
  prod_img_rumi: z.string(),
  _federation: FederationSchema,
  _formatted: FormattedSchema,
});

// Zod схема для Result
const ResultSchema = z.object({
  data: z.array(DaumSchema),
});

// Zod схема для ApiResponse
export const ApiResponseSchema = z.object({
  result: ResultSchema,
});

// Выведенные типы из Zod схемы
export type ApiResponse = z.infer<typeof ApiResponseSchema>;
export type Result = z.infer<typeof ResultSchema>;
export type Daum = z.infer<typeof DaumSchema>;
export type Federation = z.infer<typeof FederationSchema>;
export type Formatted = z.infer<typeof FormattedSchema>;

const apiURL = process.env.SEARCH_API_URL;
console.log("🚀 ~ apiURL:", apiURL);
export const searchTool = createTool({
  id: "Seach in shop catalog",
  inputSchema: MultiSearchSchema,
  outputSchema: ApiResponseSchema,
  description: `Search in catalog by MeiliSearch engine with filters and sort`,
  execute: async ({ context }) => {
    const result = await search(context);
    return result as ApiResponse;
  },
});
async function search(queries: MultiSearchQueries): ApiResponse | unknown {
  const input = JSON.stringify(queries);
  console.log("🚀 ~ search ~ input:", input);

  const url = `${apiURL}?input=${input}`;
  console.log("🌐 URL запроса:", url);

  try {
    const res = await fetch(url);
    console.log("📡 Статус ответа:", res.status);
    const data = (await res.json()) as ApiResponse;
    console.log("🚀 ~ getSearchResults ~ data:", data);
    return data;
  } catch (error) {
    console.error("❌ Ошибка при запросе к API:", error);
    throw error;
  }
}
