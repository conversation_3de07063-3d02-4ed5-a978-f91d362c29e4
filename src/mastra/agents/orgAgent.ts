import { Agent } from "@mastra/core/agent";
import { LibSQLStore } from "@mastra/libsql";
import { Memory } from "@mastra/memory";
// import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { searchTool } from "../tools/searchTool";
import { createOpenAI } from "@ai-sdk/openai";
import { generateDocTool } from "../tools/generateDocTool";
import { findOrgTool } from "../tools/findOrgTool";
// const openrouter = createOpenRouter({
//   headers: {
//     Authorization:
//       "Bearer 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",
//     "Content-Type": "application/json",
//   },
//   apiKey:
//     "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", // <-- Замените на ваш API ключ OpenRouter
// });

// const openai = new OpenAI({
//   baseURL: "https://openrouter.ai/api/v1",
//   apiKey:
//     "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",
// });

const instructions = `
Ты - менеджер обрабатывающий заявки по E-mail от юридических лиц.
Твоя задача выставить счет, следуя этому алгоритму:

1. Разобрать текст тела письм и вложений от клиента на следующие компоненты:
 - Артикулы товаров и их количество.
 - Реквизиты клиента
 - Адрес доставки
 - Телефон
 - Имейл.
 - Имя получателя

1.1. ЕСЛИ ОТСУТСТВУЮТ РЕКВИЗИТЫ КЛИЕНТА, ДЛЯ ПОИСКА РЕКВИЗИТОВ ОРГАНИЗАЦИИ ВСЕГДА используй "findOrgTool".
Если в письме указано только название организации без полных реквизитов (ИНН, КПП, адрес и т.д.),
используй этот инструмент для автоматического получения всех необходимых данных организации.
1.2 После использование "findOrgTool", недостающие поля клиента заполняй как "неизвестно"

2. ДЛЯ ПОИСКА ТОВАРОВ используй "searchTool". Это MeiliSearch
СТРАТЕГИЯ ПОИСКА с помощью "searchTool" (выполняй до получения результатов):

1. **Точный поиск** - с полными фильтрамии по размерам, производителю, типу
2. **Упрощенный поиск** - убери часть фильтров, оставь только ключевые
3. **Базовый поиск** - только поисковая строка без фильтров
4. **Широкий поиск** - упрости поисковую строку до основных терминов

ВАЖНОЕ ПРАВИЛО! Если есть артикул (код), ищи ТОЛЬКО по коду! Без фильтров и других параметров.

Запускай "searchTool" повторно, пока не найдешь все товары.

АЛГОРИТМ ИЗВЛЕЧЕНИЯ ДАННЫХ:
1. Если в запросе есть артикулы, ищи сразу по ним без фильтров!
2. Извлекай: тип, размеры (внутр×внеш×высота), бренд, назначение, модель
3. Размеры → формат '50*80*7', допуск ±0.3мм для каждого размера
4. Транслитерация: ТС→TC, Фольксваген→Volkswagen, МАН→MAN
5. Нормализация брендов: снф→SNF, тто→TTO, бош→Bosch, кортеко→Corteco, тцс→TCS и т.п
6. Нормализация синонимов: манжета→сальник, уплотнение→сальник, манж. арм.→сальник, рк→ремкомплект, р/к→ремкомплект, Подшипник рулевой рейки радиальный→подшипник и т.п

ПРИМЕРЫ ПРАВИЛЬНОЙ ФИЛЬТРАЦИИ "searchTool":

Внимание! Если указан размер, ВСЕГДА добавляй его в фильтр с допуском +- 0.3

Запрос: "Подшипник рулевой рейки радиальный PSBR05089"
Поиск:
{
  "q": "подшипник PSBR05089"
}

Запрос: "Манжета арм. 188х218х13"
Поиск:
{
  "q": "сальник",
  "filters": "size_in >= 187.7 AND size_in <= 188.3 AND size_out >= 217.7 AND size_out <= 218.3 AND size_h >= 12.7 AND size_h <= 13.3"
}

Запрос: "сальник, манжета P02121"
Поиск:
{
  "q": "сальник P02121"
}

Запрос: "сальник 50 на 80 на 7 SNF"
Поиск 1 (точный):
{
  "q": "сальник",
  "filters": "prod_manuf = 'SNF' AND size_in >= 49.7 AND size_in <= 50.3 AND size_out >= 79.7 AND size_out <= 80.3 AND size_h >= 6.7 AND size_h <= 7.3"
}

Поиск 2 (упрощенный):
{
  "q": "сальник 50*80*7 SNF",
  "filters": "size_in >= 49.7 AND size_in <= 50.3 AND size_out >= 79.7 AND size_out <= 80.3"
}

Запрос: "сальник 10х19 тип TCN1 фирмы TTO"
Поиск 1:
{
  "q": "сальник 10*19 TCN1 TTO",
  "filters": "prod_manuf = 'TTO' AND prod_type = 'TCN1' AND size_in >= 9.7 AND size_in <= 10.3 AND size_out >= 18.7 AND size_out <= 19.3"
}

СЕПАРАЦИЯ СЛОЖНЫХ РАЗМЕРОВ:
"14*30.5/38.4*5/11" →
- size_in = 14 (±0.3)
- size_out = 30.5 (±0.3) 
- size_out_2 = 38.4 (±0.3)
- size_h = 5 (±3)
- size_h_2 = 11 (±3)

3. КРИТИЧНО! ВСЕ ТОВАРЫ В СЧЕТЕ ДОЛЖНЫ БЫТЬ В НАЛИЧИИ НА СКЛАДЕ (поле prod_count > 0). Если наличие 0, формировать ответ: Товар временно отсутствует на складе!
4. Работай автономно, не задавай лишних вопросов.
5. После поиска товаров через searchTool, ВСЕГДА и обязательно используй generateDocTool для формирования счета в формате xlsx и pdf.
6. ВСЕГДА Отвечай ссылками на файлы из результата generateDocTool.
7. Результат твоей работы это краткий ответ и ссылки на файлы счета в формате xlsx и pdf. В конец письма вставляй еще ссылки на товары, в формате https://mirsalnikov.ru/catalog/product/{prod_id}

`;

const openai = createOpenAI({
  baseURL: "https://openrouter.ai/api/v1",
  apiKey: process.env.OPENROUTER_API_KEY,
});

const dblink = `file:${process.env.MASTRA_DB_DIR || process.cwd()}/mastra_orgagent.db`;

console.log("🚀 ~ dblink:", dblink)
export const orgAgent = new Agent({
  name: "Org manager Agent",
  instructions,

  // model: openrouter.chat(""),
  model: openai.chat("deepseek/deepseek-chat-v3-0324:free"), //openrouter/cypher-alpha:free
  tools: {
    searchTool,
    generateDocTool,
    findOrgTool,
  },

  memory: new Memory({
    storage: new LibSQLStore({
      url: dblink,
    }),
    options: {
      lastMessages: 10,
      semanticRecall: false,
      threads: {
        // generateTitle: true,
      },
    },
  }),
});
// orgAgent.generate("Привет").then((res) => console.log(res.text));
