import { createTool } from "@mastra/core/tools";
import { z } from "zod";

const apiURL = process.env.DOC_API_URL;

export const ClientSchema = z.object({
  fullorgname: z.string().describe("Полное наименование организации"),
  client_name: z.string().describe("ФИО контактного лица"),
  client_mail: z.string().describe("E-mail контактного лица"),
  client_phone: z.string().describe("Телефон контактного лица"),
  client_country: z
    .string()
    .optional()
    .default("Россия")
    .describe("Страна, по умолчанию: Россия"),
  client_city: z.string().optional().describe("Город"),
  client_street: z.string().optional().describe("Улица"),
  client_house: z.string().optional().describe("Дом"),
  client_postindex: z.string().optional().describe("Почтовый Индекс"),
});
export type Client = z.infer<typeof ClientSchema>;

export const OrgSchema = z.object({
  org_name: z.string().describe("Название организации"),
  org_adress: z.string().optional().describe("Адрес организации"),
  org_inn: z.string().optional().describe("ИНН организации"),
  org_kpp: z.string().optional().describe("КПП организации"),
  org_rschet: z.string().optional().describe("Расчетный счет организации"),
  org_kschet: z
    .string()
    .optional()
    .describe("Корреспондентский счет организации"),
  org_bik: z.string().optional().describe("БИК банка организации"),
  org_bank: z.string().optional().describe("Банк организации"),
});
export type Org = z.infer<typeof OrgSchema>;

export const ProductSchema = z.object({
  prod_sku: z.string(),
  prod_analogsku: z.string(),
  prod_price: z.number(),
  prod_manuf: z.string(),
  prod_type: z.string(),
  prod_size: z.string(),
  prod_purpose: z.string(),
  //   qty: z.number(),
});
export type Product = z.infer<typeof ProductSchema>;

export const ProductDatumSchema = z.object({
  id: z.number(),
  qty: z.number(),
  product: ProductSchema,
});
export type ProductDatum = z.infer<typeof ProductDatumSchema>;

export const OfferPayloadSchema = z.object({
  countryId: z
    .number()
    .optional()
    .default(643)
    .describe("ID страны, по умолчанию: 643 (Россия)"),
  //   filename: z.string().default("99_demo_schet_ip_igolkin.xlsx").optional(),
  shippingIndex: z.number().default(101000).optional(),
  //   shippingPrice: z.null().optional(),
  shippingType: z.enum(["express", "standard"]).default("express").optional(),
  productData: z.array(ProductDatumSchema),
  client: ClientSchema,
  org: OrgSchema,
});
export type OfferPayload = z.infer<typeof OfferPayloadSchema>;

export const OfferResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  files: z.object({
    xlsx: z.object({
      url: z.string(),
      fileName: z.string(),
    }),
    pdf: z
      .object({
        url: z.string(),
        fileName: z.string(),
      })
      .nullable(),
  }),
});
export type OfferResponse = z.infer<typeof OfferResponseSchema>;

export const generateDocTool = createTool({
  id: "generate xlsx document",
  inputSchema: OfferPayloadSchema,
  outputSchema: OfferResponseSchema,
  description: `generate xlsx document by data`,
  execute: async ({ context }) => {
    const result = await generateDoc(context);
    return result as OfferResponse;
  },
});
async function generateDoc(payload: OfferPayload) {
  const input = JSON.stringify({
    ...payload,
    filename: payload.filename || "99_demo_schet_ip_igolkin.xlsx",
    fileResponse: true,
  });

  console.log("🚀 ~ generateDoc ~ input:", input);
  // https://api.mirsalnikov.ru/service/documents/offer/
  try {
    const res = await fetch(`${apiURL}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: input,
    });
    console.log("📡 Статус ответа:", res.status);
    const data = (await res.json()) as OfferResponse;
    console.log("🚀 ~ generate xlsx doc ~ data:", data);
    return data;
  } catch (error) {
    console.error("❌ Ошибка при запросе к API:", error);
    throw error;
  }
}
