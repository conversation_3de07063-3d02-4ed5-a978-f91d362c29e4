# Документация по интеграции orgAgent во фронтенд

## Обзор системы

### Что такое orgAgent?

orgAgent - это специализированный AI-агент на базе Mastra Framework, предназначенный для обработки заявок от юридических лиц по email. Агент автоматически:

1. **Извлекает данные из писем:**
   - Артикулы товаров и количество
   - Реквизиты клиента (ИНН, КПП, адрес, телефон, email)
   - Адрес доставки
   - Имя получателя

2. **Использует специализированные инструменты:**
   - `findOrgTool` - поиск реквизитов организации через API mirsalnikov.ru
   - `searchTool` - поиск товаров в каталоге через MeiliSearch
   - `generateDocTool` - генерация коммерческих предложений в форматах XLSX и PDF

3. **Обладает памятью:**
   - Использует LibSQL для хранения истории разговоров
   - Поддерживает контекст последних 10 сообщений
   - Автоматически генерирует заголовки для тредов

## Архитектура фронтенда

### Технический стек

```json
// Frontend dependencies (package.json)
{
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "axios": "^1.6.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.0.0",
    "@vueuse/core": "^10.0.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.0.0",
    "vite": "^5.0.0",
    "@vue/tsconfig": "^0.4.0",
    "vue-tsc": "^1.8.0"
  }
}

// Backend dependencies (отдельный package.json для сервера)
{
  "dependencies": {
    "@mastra/core": "^0.10.10",
    "@mastra/libsql": "^0.10.3",
    "@mastra/memory": "^0.10.4",
    "@ai-sdk/openai": "^1.3.23",
    "@openrouter/ai-sdk-provider": "^0.7.2",
    "express": "^4.18.0",
    "cors": "^2.8.5",
    "multer": "^1.4.5",
    "zod": "^3.25.75",
    "dotenv": "^16.0.0"
  },
  "devDependencies": {
    "@types/express": "^4.17.0",
    "@types/cors": "^2.8.0",
    "@types/multer": "^1.4.0",
    "nodemon": "^3.0.0",
    "typescript": "^5.0.0"
  }
}
```

### Структура проекта

```
project/
├── frontend/                    # Vue.js фронтенд
│   ├── src/
│   │   ├── components/
│   │   │   ├── Chat/
│   │   │   │   ├── ChatInterface.vue
│   │   │   │   ├── MessageList.vue
│   │   │   │   ├── MessageInput.vue
│   │   │   │   └── FileUpload.vue
│   │   │   ├── Sessions/
│   │   │   │   ├── SessionList.vue
│   │   │   │   ├── SessionItem.vue
│   │   │   │   └── NewSessionButton.vue
│   │   │   └── Layout/
│   │   │       ├── Sidebar.vue
│   │   │       └── MainLayout.vue
│   │   ├── composables/
│   │   │   ├── useOrgAgent.ts
│   │   │   ├── useSessions.ts
│   │   │   └── useChat.ts
│   │   ├── services/
│   │   │   └── apiClient.ts
│   │   ├── stores/
│   │   │   ├── chatStore.ts
│   │   │   └── sessionStore.ts
│   │   ├── types/
│   │   │   ├── agent.ts
│   │   │   ├── session.ts
│   │   │   └── message.ts
│   │   ├── utils/
│   │   │   ├── formatters.ts
│   │   │   └── validators.ts
│   │   ├── router/
│   │   │   └── index.ts
│   │   ├── App.vue
│   │   └── main.ts
│   ├── public/
│   ├── package.json
│   ├── vite.config.ts
│   └── tailwind.config.js
├── backend/                     # Express + Mastra сервер
│   ├── src/
│   │   ├── server/
│   │   │   └── server.ts
│   │   └── mastra/
│   │       ├── agents/
│   │       │   └── orgAgent.ts
│   │       └── tools/
│   │           ├── searchTool.ts
│   │           ├── findOrgTool.ts
│   │           └── generateDocTool.ts
│   ├── uploads/                 # Временные файлы
│   ├── data/                    # База данных
│   ├── package.json
│   └── tsconfig.json
└── docs/
```

## Интеграция с бэкендом

### 1. API клиент для фронтенда

```typescript
// src/services/apiClient.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

// Типы для orgAgent
export interface OrgAgentRequest {
  message: string;
  threadId?: string;
  attachments?: File[];
}

export interface OrgAgentResponse {
  message: string;
  threadId: string;
  files?: {
    xlsx?: { url: string; fileName: string };
    pdf?: { url: string; fileName: string };
  };
  toolCalls?: Array<{
    toolName: string;
    input: any;
    output: any;
  }>;
}

export interface ChatSession {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  lastMessage?: string;
}

export interface ChatMessage {
  id: string;
  threadId: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  files?: Array<{ url: string; fileName: string; type: 'xlsx' | 'pdf' }>;
  toolCalls?: Array<{
    toolName: string;
    input: any;
    output: any;
  }>;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  // Отправка сообщения агенту
  async sendMessage(request: OrgAgentRequest): Promise<OrgAgentResponse> {
    const formData = new FormData();
    formData.append('message', request.message);

    if (request.threadId) {
      formData.append('threadId', request.threadId);
    }

    // Добавляем файлы если есть
    request.attachments?.forEach((file, index) => {
      formData.append(`attachment_${index}`, file);
    });

    const response = await fetch(`${this.baseUrl}/api/agent/message`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Ошибка отправки сообщения');
    }

    const data = await response.json();
    return data.data;
  }

  // Стриминг сообщений
  async *streamMessage(request: Omit<OrgAgentRequest, 'attachments'>): AsyncGenerator<string> {
    const response = await fetch(`${this.baseUrl}/api/agent/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error('Ошибка стриминга');
    }

    const reader = response.body?.getReader();
    if (!reader) throw new Error('Нет reader для стрима');

    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data.trim()) {
              try {
                const parsed = JSON.parse(data);
                if (parsed.content) {
                  yield parsed.content;
                }
              } catch (e) {
                // Игнорируем ошибки парсинга
              }
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  // Получение списка сессий
  async getSessions(): Promise<ChatSession[]> {
    const response = await fetch(`${this.baseUrl}/api/sessions`);

    if (!response.ok) {
      throw new Error('Ошибка загрузки сессий');
    }

    const data = await response.json();
    return data.sessions;
  }

  // Создание новой сессии
  async createSession(): Promise<ChatSession> {
    const response = await fetch(`${this.baseUrl}/api/sessions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Ошибка создания сессии');
    }

    const data = await response.json();
    return data.session;
  }

  // Удаление сессии
  async deleteSession(sessionId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/sessions/${sessionId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Ошибка удаления сессии');
    }
  }

  // Получение истории сообщений
  async getMessages(threadId: string): Promise<ChatMessage[]> {
    const response = await fetch(`${this.baseUrl}/api/sessions/${threadId}/messages`);

    if (!response.ok) {
      throw new Error('Ошибка загрузки сообщений');
    }

    const data = await response.json();
    return data.messages;
  }
}

export const apiClient = new ApiClient();
```

### 2. Composable для работы с orgAgent

```typescript
// src/composables/useOrgAgent.ts
import { ref } from 'vue';
import { apiClient, type OrgAgentRequest, type OrgAgentResponse } from '../services/apiClient';

export const useOrgAgent = () => {
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const sendMessage = async (
    message: string,
    threadId?: string,
    attachments?: File[]
  ): Promise<OrgAgentResponse> => {
    isLoading.value = true;
    error.value = null;

    try {
      const request: OrgAgentRequest = {
        message,
        threadId,
        attachments,
      };

      const response = await apiClient.sendMessage(request);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Произошла ошибка';
      error.value = errorMessage;
      throw new Error(errorMessage);
    } finally {
      isLoading.value = false;
    }
  };

  const streamMessage = async function* (
    message: string,
    threadId?: string
  ) {
    isLoading.value = true;
    error.value = null;

    try {
      const stream = apiClient.streamMessage({ message, threadId });

      for await (const chunk of stream) {
        yield chunk;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Ошибка стриминга';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    sendMessage,
    streamMessage,
    isLoading,
    error,
  };
};
```

### 3. Composable для управления сессиями

```typescript
// src/composables/useSessions.ts
import { ref, onMounted } from 'vue';
import { apiClient, type ChatSession } from '../services/apiClient';

export const useSessions = () => {
  const sessions = ref<ChatSession[]>([]);
  const currentSessionId = ref<string | null>(null);
  const isLoading = ref(false);

  // Загрузка списка сессий
  const loadSessions = async () => {
    isLoading.value = true;
    try {
      const sessionList = await apiClient.getSessions();
      sessions.value = sessionList;
    } catch (error) {
      console.error('Ошибка загрузки сессий:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // Создание новой сессии
  const createSession = async (): Promise<string> => {
    const session = await apiClient.createSession();

    await loadSessions(); // Обновляем список
    currentSessionId.value = session.id;
    return session.id;
  };

  // Удаление сессии
  const deleteSession = async (sessionId: string) => {
    await apiClient.deleteSession(sessionId);
    await loadSessions();

    if (currentSessionId.value === sessionId) {
      currentSessionId.value = null;
    }
  };

  // Установка текущей сессии
  const setCurrentSessionId = (sessionId: string | null) => {
    currentSessionId.value = sessionId;
  };

  onMounted(() => {
    loadSessions();
  });

  return {
    sessions,
    currentSessionId,
    setCurrentSessionId,
    createSession,
    deleteSession,
    loadSessions,
    isLoading,
  };
};
```

## Компоненты интерфейса

### 1. Основной чат-интерфейс

```vue
<!-- src/components/Chat/ChatInterface.vue -->
<template>
  <div class="flex flex-col h-full">
    <div class="flex-1 overflow-hidden">
      <MessageList
        :messages="messages"
        :is-loading="isLoading"
      />
    </div>

    <div class="border-t bg-white p-4">
      <MessageInput
        @send-message="handleSendMessage"
        :disabled="isLoading"
      />
    </div>

    <div
      v-if="error"
      class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded"
    >
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useOrgAgent } from '../../composables/useOrgAgent';
import { useSessions } from '../../composables/useSessions';
import { apiClient } from '../../services/apiClient';
import MessageList from './MessageList.vue';
import MessageInput from './MessageInput.vue';

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  files?: Array<{ url: string; fileName: string; type: 'xlsx' | 'pdf' }>;
  toolCalls?: Array<{
    toolName: string;
    input: any;
    output: any;
  }>;
}

const messages = ref<Message[]>([]);
const { sendMessage, isLoading, error } = useOrgAgent();
const { currentSessionId, createSession } = useSessions();

// Загрузка истории сообщений при смене сессии
watch(currentSessionId, async (newSessionId) => {
  if (newSessionId) {
    await loadMessageHistory(newSessionId);
  } else {
    messages.value = [];
  }
});

const loadMessageHistory = async (threadId: string) => {
  try {
    const history = await apiClient.getMessages(threadId);
    const formattedMessages = history.map(msg => ({
      id: msg.id,
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
      timestamp: new Date(msg.timestamp),
      files: msg.files,
      toolCalls: msg.toolCalls,
    }));
    messages.value = formattedMessages;
  } catch (error) {
    console.error('Ошибка загрузки истории:', error);
  }
};

const handleSendMessage = async (
  content: string,
  attachments?: File[]
) => {
  let sessionId = currentSessionId.value;

  // Создаем новую сессию если её нет
  if (!sessionId) {
    sessionId = await createSession();
  }

  // Добавляем сообщение пользователя
  const userMessage: Message = {
    id: Date.now().toString(),
    role: 'user',
    content,
    timestamp: new Date(),
  };
  messages.value.push(userMessage);

  try {
    // Отправляем сообщение агенту
    const response = await sendMessage(content, sessionId, attachments);

    // Добавляем ответ агента
    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: response.message,
      timestamp: new Date(),
      files: response.files ? [
        ...(response.files.xlsx ? [{ ...response.files.xlsx, type: 'xlsx' as const }] : []),
        ...(response.files.pdf ? [{ ...response.files.pdf, type: 'pdf' as const }] : []),
      ] : undefined,
      toolCalls: response.toolCalls,
    };

    messages.value.push(assistantMessage);
  } catch (error) {
    console.error('Ошибка отправки сообщения:', error);
  }
};
</script>

### 2. Компонент списка сообщений

```vue
<!-- src/components/Chat/MessageList.vue -->
<template>
  <div class="flex-1 overflow-y-auto p-4 space-y-4">
    <div
      v-for="message in messages"
      :key="message.id"
      :class="[
        'flex',
        message.role === 'user' ? 'justify-end' : 'justify-start'
      ]"
    >
      <div
        :class="[
          'max-w-3xl rounded-lg px-4 py-2',
          message.role === 'user'
            ? 'bg-blue-500 text-white'
            : 'bg-gray-100 text-gray-900'
        ]"
      >
        <div class="whitespace-pre-wrap">{{ message.content }}</div>

        <!-- Отображение файлов -->
        <div
          v-if="message.files && message.files.length > 0"
          class="mt-2 space-y-1"
        >
          <a
            v-for="(file, index) in message.files"
            :key="index"
            :href="file.url"
            target="_blank"
            rel="noopener noreferrer"
            class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 hover:bg-green-200"
          >
            📄 {{ file.fileName }}
          </a>
        </div>

        <!-- Отображение вызовов инструментов -->
        <details
          v-if="message.toolCalls && message.toolCalls.length > 0"
          class="mt-2"
        >
          <summary class="cursor-pointer text-sm opacity-70">
            Использованные инструменты ({{ message.toolCalls.length }})
          </summary>
          <div class="mt-1 space-y-1 text-xs">
            <div
              v-for="(call, index) in message.toolCalls"
              :key="index"
              class="bg-black bg-opacity-10 rounded p-2"
            >
              <div class="font-semibold">{{ call.toolName }}</div>
              <div class="opacity-70">
                Вход: {{ JSON.stringify(call.input, null, 2) }}
              </div>
            </div>
          </div>
        </details>

        <div class="text-xs opacity-50 mt-1">
          {{ message.timestamp.toLocaleTimeString() }}
        </div>
      </div>
    </div>

    <div v-if="isLoading" class="flex justify-start">
      <div class="bg-gray-100 rounded-lg px-4 py-2">
        <div class="flex items-center space-x-2">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
          <span>Обрабатываю запрос...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Message } from './ChatInterface.vue';

interface Props {
  messages: Message[];
  isLoading: boolean;
}

defineProps<Props>();
</script>
```

### 3. Компонент ввода сообщений

```vue
<!-- src/components/Chat/MessageInput.vue -->
<template>
  <form @submit.prevent="handleSubmit" class="space-y-2">
    <!-- Отображение прикрепленных файлов -->
    <div v-if="attachments.length > 0" class="flex flex-wrap gap-2">
      <div
        v-for="(file, index) in attachments"
        :key="index"
        class="flex items-center bg-gray-100 rounded-lg px-3 py-1 text-sm"
      >
        <span class="mr-2">📎 {{ file.name }}</span>
        <button
          type="button"
          @click="removeAttachment(index)"
          class="text-red-500 hover:text-red-700"
        >
          ×
        </button>
      </div>
    </div>

    <div class="flex items-end space-x-2">
      <div class="flex-1">
        <textarea
          v-model="message"
          placeholder="Опишите ваш запрос или прикрепите файл с заявкой..."
          class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows="3"
          :disabled="disabled"
          @keydown.enter.exact.prevent="handleSubmit"
        />
      </div>

      <div class="flex flex-col space-y-2">
        <button
          type="button"
          @click="$refs.fileInput.click()"
          class="p-2 text-gray-500 hover:text-gray-700 border border-gray-300 rounded-lg"
          :disabled="disabled"
        >
          📎
        </button>

        <button
          type="submit"
          :disabled="disabled || (!message.trim() && attachments.length === 0)"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Отправить
        </button>
      </div>
    </div>

    <input
      ref="fileInput"
      type="file"
      multiple
      @change="handleFileSelect"
      class="hidden"
      accept=".pdf,.doc,.docx,.txt,.xlsx,.xls"
    />
  </form>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  disabled: boolean;
}

interface Emits {
  (e: 'send-message', message: string, attachments?: File[]): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const message = ref('');
const attachments = ref<File[]>([]);

const handleSubmit = () => {
  if (message.value.trim() || attachments.value.length > 0) {
    emit('send-message', message.value.trim(), attachments.value);
    message.value = '';
    attachments.value = [];
  }
};

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const files = Array.from(target.files || []);
  attachments.value.push(...files);
};

const removeAttachment = (index: number) => {
  attachments.value.splice(index, 1);
};
</script>

### 4. Pinia Store для управления состоянием

```typescript
// src/stores/sessionStore.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { apiClient, type ChatSession } from '../services/apiClient';

export const useSessionStore = defineStore('session', () => {
  const sessions = ref<ChatSession[]>([]);
  const currentSessionId = ref<string | null>(null);
  const isLoading = ref(false);

  const currentSession = computed(() =>
    sessions.value.find(s => s.id === currentSessionId.value)
  );

  const loadSessions = async () => {
    isLoading.value = true;
    try {
      const sessionList = await apiClient.getSessions();
      sessions.value = sessionList;
    } catch (error) {
      console.error('Ошибка загрузки сессий:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const createSession = async (): Promise<string> => {
    const session = await apiClient.createSession();
    await loadSessions();
    currentSessionId.value = session.id;
    return session.id;
  };

  const deleteSession = async (sessionId: string) => {
    await apiClient.deleteSession(sessionId);
    await loadSessions();

    if (currentSessionId.value === sessionId) {
      currentSessionId.value = null;
    }
  };

  const setCurrentSessionId = (sessionId: string | null) => {
    currentSessionId.value = sessionId;
  };

  return {
    sessions,
    currentSessionId,
    currentSession,
    isLoading,
    loadSessions,
    createSession,
    deleteSession,
    setCurrentSessionId,
  };
});
```

### 5. Компонент управления сессиями

```vue
<!-- src/components/Sessions/SessionList.vue -->
<template>
  <div class="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
    <div class="p-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Чаты</h2>
      <NewSessionButton @create-session="createSession" />
    </div>

    <div class="flex-1 overflow-y-auto">
      <div v-if="isLoading" class="p-4 text-center text-gray-500">
        Загрузка сессий...
      </div>

      <div v-else-if="sessions.length === 0" class="p-4 text-center text-gray-500">
        Нет активных чатов
      </div>

      <div v-else class="space-y-1 p-2">
        <SessionItem
          v-for="session in sessions"
          :key="session.id"
          :session="session"
          :is-active="session.id === currentSessionId"
          @select="setCurrentSessionId(session.id)"
          @delete="deleteSession(session.id)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useSessionStore } from '../../stores/sessionStore';
import SessionItem from './SessionItem.vue';
import NewSessionButton from './NewSessionButton.vue';

const {
  sessions,
  currentSessionId,
  isLoading,
  loadSessions,
  createSession,
  deleteSession,
  setCurrentSessionId
} = useSessionStore();

onMounted(() => {
  loadSessions();
});
</script>

<!-- src/components/Sessions/SessionItem.vue -->
<template>
  <div
    :class="[
      'group relative p-3 rounded-lg cursor-pointer transition-colors',
      isActive
        ? 'bg-blue-100 border border-blue-200'
        : 'hover:bg-gray-100'
    ]"
    @click="$emit('select')"
  >
    <div class="flex justify-between items-start">
      <div class="flex-1 min-w-0">
        <h3 class="font-medium text-gray-900 truncate">
          {{ session.title }}
        </h3>
        <p
          v-if="session.lastMessage"
          class="text-sm text-gray-500 truncate mt-1"
        >
          {{ session.lastMessage }}
        </p>
        <div class="flex items-center justify-between mt-2 text-xs text-gray-400">
          <span>{{ formatDate(new Date(session.updatedAt)) }}</span>
          <span>{{ session.messageCount }} сообщений</span>
        </div>
      </div>

      <button
        @click.stop="$emit('delete')"
        class="opacity-0 group-hover:opacity-100 ml-2 p-1 text-gray-400 hover:text-red-500 transition-opacity"
      >
        🗑️
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ChatSession } from '../../services/apiClient';

interface Props {
  session: ChatSession;
  isActive: boolean;
}

interface Emits {
  (e: 'select'): void;
  (e: 'delete'): void;
}

defineProps<Props>();
defineEmits<Emits>();

const formatDate = (date: Date) => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return 'Сегодня';
  if (diffDays === 1) return 'Вчера';
  if (diffDays < 7) return `${diffDays} дн. назад`;
  return date.toLocaleDateString('ru-RU');
};
</script>

<!-- src/components/Sessions/NewSessionButton.vue -->
<template>
  <button
    @click="handleCreate"
    class="w-full mt-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
  >
    + Новый чат
  </button>
</template>

<script setup lang="ts">
interface Emits {
  (e: 'create-session'): void;
}

const emit = defineEmits<Emits>();

const handleCreate = async () => {
  try {
    emit('create-session');
  } catch (error) {
    console.error('Ошибка создания новой сессии:', error);
  }
};
</script>
```

## Настройка бэкенда (Express + Mastra)

### 1. Настройка Express сервера с Mastra

```typescript
// src/server/server.ts
import express from 'express';
import cors from 'cors';
import multer from 'multer';
import { orgAgent } from '../mastra/agents/orgAgent';

const app = express();
const upload = multer({ dest: 'uploads/' });

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Эндпоинт для отправки сообщения агенту
app.post('/api/agent/message', upload.array('attachments'), async (req, res) => {
  try {
    const { message, threadId } = req.body;
    const files = req.files as Express.Multer.File[];

    // Подготавливаем контекст для агента
    let fullMessage = message;

    // Если есть файлы, добавляем информацию о них в сообщение
    if (files && files.length > 0) {
      const fileInfo = files.map(f => `Файл: ${f.originalname} (${f.mimetype})`).join('\n');
      fullMessage = `${message}\n\nПрикрепленные файлы:\n${fileInfo}`;
    }

    // Вызываем агента
    const response = await orgAgent.generate([
      { role: 'user', content: fullMessage }
    ], {
      threadId: threadId || undefined,
    });

    res.json({
      success: true,
      data: {
        message: response.text,
        threadId: response.threadId,
        files: response.files,
        toolCalls: response.toolCalls,
      },
    });
  } catch (error) {
    console.error('Ошибка обработки сообщения:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Внутренняя ошибка сервера',
    });
  }
});

// Эндпоинт для стриминга ответов
app.post('/api/agent/stream', async (req, res) => {
  try {
    const { message, threadId } = req.body;

    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // Стримим ответ от агента
    const stream = await orgAgent.stream([
      { role: 'user', content: message }
    ], {
      threadId: threadId || undefined,
    });

    for await (const chunk of stream) {
      res.write(`data: ${JSON.stringify({ content: chunk.text })}\n\n`);
    }

    res.write(`data: [DONE]\n\n`);
    res.end();
  } catch (error) {
    console.error('Ошибка стриминга:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Ошибка стриминга',
    });
  }
});

// Эндпоинты для управления сессиями
app.get('/api/sessions', async (req, res) => {
  try {
    // Получаем список тредов из памяти агента
    const memory = orgAgent.getMemory();
    if (!memory) {
      return res.json({ sessions: [] });
    }

    const threads = await memory.getThreads();
    const sessions = threads.map(thread => ({
      id: thread.id,
      title: thread.title || 'Новый чат',
      createdAt: thread.createdAt,
      updatedAt: thread.updatedAt,
      messageCount: thread.messages?.length || 0,
      lastMessage: thread.messages?.[thread.messages.length - 1]?.content,
    }));

    res.json({ sessions });
  } catch (error) {
    console.error('Ошибка получения сессий:', error);
    res.status(500).json({
      success: false,
      error: 'Ошибка получения сессий',
    });
  }
});

app.post('/api/sessions', async (req, res) => {
  try {
    const memory = orgAgent.getMemory();
    if (!memory) {
      throw new Error('Память агента не настроена');
    }

    const thread = await memory.createThread({
      resourceId: 'orgAgent',
      metadata: { type: 'orgAgent_chat' }
    });

    res.json({
      success: true,
      session: {
        id: thread.id,
        title: 'Новый чат',
        createdAt: thread.createdAt,
        updatedAt: thread.updatedAt,
        messageCount: 0,
      },
    });
  } catch (error) {
    console.error('Ошибка создания сессии:', error);
    res.status(500).json({
      success: false,
      error: 'Ошибка создания сессии',
    });
  }
});

app.delete('/api/sessions/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const memory = orgAgent.getMemory();

    if (memory) {
      await memory.deleteThread(sessionId);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Ошибка удаления сессии:', error);
    res.status(500).json({
      success: false,
      error: 'Ошибка удаления сессии',
    });
  }
});

app.get('/api/sessions/:threadId/messages', async (req, res) => {
  try {
    const { threadId } = req.params;
    const memory = orgAgent.getMemory();

    if (!memory) {
      return res.json({ messages: [] });
    }

    const messages = await memory.getMessages(threadId);

    res.json({
      messages: messages.map(msg => ({
        id: msg.id,
        threadId: msg.threadId,
        role: msg.role,
        content: msg.content,
        timestamp: msg.createdAt,
        files: msg.files,
        toolCalls: msg.toolCalls,
      })),
    });
  } catch (error) {
    console.error('Ошибка получения сообщений:', error);
    res.status(500).json({
      success: false,
      error: 'Ошибка получения сообщений',
    });
  }
});

const PORT = process.env.PORT || 4000;
app.listen(PORT, () => {
  console.log(`🚀 Сервер запущен на порту ${PORT}`);
});

## Типы и интерфейсы

### 1. Типы для агента

```typescript
// src/types/agent.ts
export interface ToolCall {
  toolName: 'searchTool' | 'findOrgTool' | 'generateDocTool';
  input: any;
  output: any;
  timestamp: Date;
}

export interface SearchToolInput {
  queries: Array<{
    q: string;
    filters?: string;
    sort?: string;
  }>;
}

export interface FindOrgToolInput {
  query: string; // Название организации или ИНН
}

export interface GenerateDocToolInput {
  countryId?: number;
  shippingIndex?: number;
  shippingType?: 'express' | 'standard';
  productData: Array<{
    id: number;
    qty: number;
    product: {
      prod_sku: string;
      prod_analogsku: string;
      prod_price: number;
      prod_manuf: string;
      prod_type: string;
      prod_size: string;
      prod_purpose: string;
    };
  }>;
  client: {
    fullorgname: string;
    client_name: string;
    client_mail: string;
    client_phone: string;
    client_country?: string;
    client_city?: string;
    client_street?: string;
    client_house?: string;
    client_postindex?: string;
  };
  org: {
    org_name: string;
    org_adress?: string;
    org_inn?: string;
    org_kpp?: string;
    org_rschet?: string;
    org_kschet?: string;
    org_bik?: string;
    org_bank?: string;
  };
}

export interface AgentResponse {
  message: string;
  threadId: string;
  files?: {
    xlsx?: { url: string; fileName: string };
    pdf?: { url: string; fileName: string };
  };
  toolCalls?: ToolCall[];
  metadata?: {
    processingTime: number;
    tokensUsed: number;
  };
}
```

### 2. Типы для сессий

```typescript
// src/types/session.ts
export interface ChatSession {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
  lastMessage?: string;
  metadata?: {
    type: 'orgAgent_chat';
    tags?: string[];
    clientInfo?: {
      orgName?: string;
      inn?: string;
    };
  };
}

export interface SessionFilters {
  dateFrom?: Date;
  dateTo?: Date;
  hasFiles?: boolean;
  clientOrg?: string;
}
```

### 3. Типы для сообщений

```typescript
// src/types/message.ts
export interface ChatMessage {
  id: string;
  threadId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  attachments?: MessageAttachment[];
  files?: GeneratedFile[];
  toolCalls?: ToolCall[];
  metadata?: {
    processingTime?: number;
    tokensUsed?: number;
    error?: string;
  };
}

export interface MessageAttachment {
  id: string;
  name: string;
  size: number;
  mimeType: string;
  url?: string;
}

export interface GeneratedFile {
  url: string;
  fileName: string;
  type: 'xlsx' | 'pdf';
  size?: number;
  generatedAt: Date;
}
```

## Утилиты и хелперы

### 1. Форматирование данных

```typescript
// src/utils/formatters.ts
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatDate = (date: Date): string => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return 'Сегодня';
  if (diffDays === 1) return 'Вчера';
  if (diffDays < 7) return `${diffDays} дн. назад`;
  return date.toLocaleDateString('ru-RU');
};

export const formatToolName = (toolName: string): string => {
  const toolNames: Record<string, string> = {
    searchTool: 'Поиск товаров',
    findOrgTool: 'Поиск реквизитов',
    generateDocTool: 'Генерация документов',
  };
  return toolNames[toolName] || toolName;
};

export const extractClientInfo = (toolCalls: ToolCall[]) => {
  const findOrgCall = toolCalls.find(call => call.toolName === 'findOrgTool');
  if (findOrgCall?.output?.data) {
    return {
      orgName: findOrgCall.output.data.name,
      inn: findOrgCall.output.data.inn,
    };
  }
  return null;
};
```

### 2. Валидация

```typescript
// src/utils/validators.ts
import { z } from 'zod';

export const messageSchema = z.object({
  content: z.string().min(1, 'Сообщение не может быть пустым').max(10000, 'Сообщение слишком длинное'),
  threadId: z.string().optional(),
});

export const attachmentSchema = z.object({
  name: z.string(),
  size: z.number().max(10 * 1024 * 1024, 'Файл слишком большой (максимум 10MB)'),
  type: z.string().refine(
    (type) => ['application/pdf', 'text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(type),
    'Неподдерживаемый тип файла'
  ),
});

export const validateMessage = (data: unknown) => {
  return messageSchema.safeParse(data);
};

export const validateAttachment = (file: File) => {
  return attachmentSchema.safeParse({
    name: file.name,
    size: file.size,
    type: file.type,
  });
};
```

## Развертывание и настройка

### 1. Конфигурация Vue проекта

```typescript
// frontend/vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
      },
    },
  },
});
```

```typescript
// frontend/src/main.ts
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createRouter, createWebHistory } from 'vue-router';
import App from './App.vue';
import './style.css';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('./components/Chat/ChatInterface.vue'),
    },
  ],
});

const app = createApp(App);
app.use(createPinia());
app.use(router);
app.mount('#app');
```

```vue
<!-- frontend/src/App.vue -->
<template>
  <div id="app" class="h-screen flex">
    <SessionList />
    <div class="flex-1">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import SessionList from './components/Sessions/SessionList.vue';
</script>

<style>
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
</style>
```

### 2. Переменные окружения

```bash
# frontend/.env
VITE_API_URL=http://localhost:4000
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.txt,.xlsx,.xls

# backend/.env
NODE_ENV=development
PORT=4000
MASTRA_DB_DIR=./data
OPENROUTER_API_KEY=your-openrouter-key
SEARCH_API_URL=your-search-api-url
DOC_API_URL=your-doc-generation-api-url
FRONTEND_URL=http://localhost:3000
```

### 3. Скрипты package.json

```json
// frontend/package.json
{
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore",
    "type-check": "vue-tsc --noEmit"
  }
}

// backend/package.json
{
  "scripts": {
    "dev": "nodemon --exec ts-node src/server/server.ts",
    "build": "tsc",
    "start": "node dist/server/server.js",
    "test": "jest"
  }
}

// root/package.json (для запуска всего проекта)
{
  "scripts": {
    "dev": "concurrently \"npm run dev --prefix backend\" \"npm run dev --prefix frontend\"",
    "build": "npm run build --prefix backend && npm run build --prefix frontend",
    "start": "concurrently \"npm run start --prefix backend\" \"npm run start --prefix frontend\"",
    "install:all": "npm install && npm install --prefix frontend && npm install --prefix backend"
  },
  "devDependencies": {
    "concurrently": "^8.0.0"
  }
}

### 3. Docker конфигурация

```dockerfile
# Dockerfile
FROM node:20-alpine

WORKDIR /app

# Копируем package.json и устанавливаем зависимости
COPY package*.json ./
RUN npm ci --only=production

# Копируем исходный код
COPY . .

# Собираем приложение
RUN npm run build

# Открываем порты
EXPOSE 3000 4000

# Запускаем приложение
CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - MASTRA_DB_DIR=/app/data
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - SEARCH_API_URL=${SEARCH_API_URL}
      - DOC_API_URL=${DOC_API_URL}
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
```

## Тестирование

### 1. Unit тесты для хуков

```typescript
// src/hooks/__tests__/useOrgAgent.test.ts
import { renderHook, act } from '@testing-library/react';
import { useOrgAgent } from '../useOrgAgent';

// Мокаем mastraClient
jest.mock('../../services/mastraClient', () => ({
  mastraClient: {
    agents: {
      generate: jest.fn(),
      stream: jest.fn(),
    },
  },
}));

describe('useOrgAgent', () => {
  it('должен отправлять сообщение и получать ответ', async () => {
    const { result } = renderHook(() => useOrgAgent());

    const mockResponse = {
      text: 'Тестовый ответ',
      threadId: 'test-thread-id',
      files: null,
      toolCalls: [],
    };

    (mastraClient.agents.generate as jest.Mock).mockResolvedValue(mockResponse);

    await act(async () => {
      const response = await result.current.sendMessage('Тестовое сообщение');
      expect(response.message).toBe('Тестовый ответ');
      expect(response.threadId).toBe('test-thread-id');
    });
  });

  it('должен обрабатывать ошибки', async () => {
    const { result } = renderHook(() => useOrgAgent());

    (mastraClient.agents.generate as jest.Mock).mockRejectedValue(
      new Error('Тестовая ошибка')
    );

    await act(async () => {
      try {
        await result.current.sendMessage('Тестовое сообщение');
      } catch (error) {
        expect(error.message).toBe('Тестовая ошибка');
      }
    });

    expect(result.current.error).toBe('Тестовая ошибка');
  });
});
```

### 2. Integration тесты

```typescript
// src/__tests__/integration/chat.test.ts
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ChatInterface from '../../components/Chat/ChatInterface';

// Мокаем хуки
jest.mock('../../hooks/useOrgAgent');
jest.mock('../../hooks/useSessions');

describe('ChatInterface Integration', () => {
  it('должен отправлять сообщение и отображать ответ', async () => {
    const mockSendMessage = jest.fn().mockResolvedValue({
      message: 'Ответ агента',
      threadId: 'test-thread',
      files: null,
      toolCalls: [],
    });

    (useOrgAgent as jest.Mock).mockReturnValue({
      sendMessage: mockSendMessage,
      isLoading: false,
      error: null,
    });

    (useSessions as jest.Mock).mockReturnValue({
      currentSessionId: 'test-session',
      createSession: jest.fn(),
    });

    render(<ChatInterface />);

    const input = screen.getByPlaceholderText(/Опишите ваш запрос/);
    const sendButton = screen.getByText('Отправить');

    fireEvent.change(input, { target: { value: 'Тестовое сообщение' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(mockSendMessage).toHaveBeenCalledWith(
        'Тестовое сообщение',
        'test-session',
        undefined
      );
    });

    await waitFor(() => {
      expect(screen.getByText('Ответ агента')).toBeInTheDocument();
    });
  });
});
```

## Мониторинг и логирование

### 1. Настройка логирования

```typescript
// src/utils/logger.ts
import winston from 'winston';

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'orgAgent-frontend' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

// Хук для логирования действий пользователя
export const useAnalytics = () => {
  const trackEvent = (event: string, properties?: Record<string, any>) => {
    logger.info('User event', { event, properties, timestamp: new Date() });

    // Интеграция с аналитикой (например, Google Analytics)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', event, properties);
    }
  };

  return { trackEvent };
};
```

### 2. Метрики производительности

```typescript
// src/utils/metrics.ts
export class PerformanceMetrics {
  private static instance: PerformanceMetrics;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMetrics {
    if (!PerformanceMetrics.instance) {
      PerformanceMetrics.instance = new PerformanceMetrics();
    }
    return PerformanceMetrics.instance;
  }

  startTimer(name: string): () => void {
    const start = performance.now();

    return () => {
      const duration = performance.now() - start;
      this.recordMetric(name, duration);
    };
  }

  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  getAverageMetric(name: string): number {
    const values = this.metrics.get(name) || [];
    return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
  }

  getMetricsReport(): Record<string, { average: number; count: number }> {
    const report: Record<string, { average: number; count: number }> = {};

    for (const [name, values] of this.metrics.entries()) {
      report[name] = {
        average: this.getAverageMetric(name),
        count: values.length,
      };
    }

    return report;
  }
}

// Хук для измерения производительности
export const usePerformanceMetrics = () => {
  const metrics = PerformanceMetrics.getInstance();

  const measureAsync = async <T>(name: string, fn: () => Promise<T>): Promise<T> => {
    const stopTimer = metrics.startTimer(name);
    try {
      const result = await fn();
      return result;
    } finally {
      stopTimer();
    }
  };

  return { measureAsync, getReport: () => metrics.getMetricsReport() };
};
```

## Заключение

Данная документация предоставляет полное руководство по интеграции orgAgent во Vue.js фронтенд-приложение. Ключевые особенности реализации:

### Основные возможности:
- ✅ Полнофункциональный чат-интерфейс с поддержкой файлов
- ✅ Управление сессиями с сохранением истории
- ✅ Интеграция со всеми инструментами orgAgent
- ✅ Отображение результатов работы инструментов
- ✅ Скачивание сгенерированных документов
- ✅ Responsive дизайн для мобильных устройств

### Технические особенности:
- 🔧 **Vue 3 + Composition API** для реактивности
- 🔧 **TypeScript** для типобезопасности
- 🔧 **Pinia** для управления состоянием
- 🔧 **Vite** для быстрой разработки
- 🔧 **Tailwind CSS** для стилизации
- 🔧 **REST API** для взаимодействия с бэкендом
- 🔧 **Server-Sent Events** для стриминга ответов
- 🔧 Обработка ошибок и валидация
- 🔧 Логирование и мониторинг

### Архитектура:
- 🏗️ **Разделение фронтенда и бэкенда** - чистая архитектура
- 🏗️ **Express + Mastra** на бэкенде для работы с агентом
- 🏗️ **Vue SFC компоненты** для переиспользования
- 🏗️ **Composables** для бизнес-логики
- 🏗️ **Pinia stores** для глобального состояния

### Готовность к продакшену:
- 🚀 Docker конфигурация
- 🚀 Nginx для проксирования
- 🚀 Переменные окружения
- 🚀 Тестирование (unit + integration)
- 🚀 Метрики производительности
- 🚀 Vite для оптимизированной сборки

### Отличия от React версии:
- 📝 **Vue SFC** вместо JSX компонентов
- 📝 **Composition API** вместо React hooks
- 📝 **Pinia** вместо React state
- 📝 **Vue Router** вместо Next.js роутинга
- 📝 **Vite** вместо Next.js сборки
- 📝 **@vueuse** для дополнительных composables

Следуя этой документации, вы сможете быстро создать полнофункциональный Vue.js фронтенд для orgAgent, аналогичный Mastra Playground, но специально адаптированный для работы с заявками юридических лиц и использующий современный Vue.js стек.
```
```
```
```
