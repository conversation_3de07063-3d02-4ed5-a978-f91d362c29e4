import { Agent } from "@mastra/core/agent";
import { LibSQLStore } from "@mastra/libsql";
import { Memory } from "@mastra/memory";
// import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { searchTool } from "../tools/searchTool";
import { createOpenAI } from "@ai-sdk/openai";
import { generateDocTool } from "../tools/generateDocTool";
import { findOrgTool } from "../tools/findOrgTool";
import { smartSearchTool } from "../tools/smartSearchTool";
// const openrouter = createOpenRouter({
//   headers: {
//     Authorization:
//       "Bearer 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",
//     "Content-Type": "application/json",
//   },
//   apiKey:
//     "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", // <-- Замените на ваш API ключ OpenRouter
// });

// const openai = new OpenAI({
//   baseURL: "https://openrouter.ai/api/v1",
//   apiKey:
//     "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",
// });

const instructions = `
Ты - менеджер обрабатывающий заявки по E-mail от юридических лиц.
ГЛАВНАЯ ЗАДАЧА: ВСЕГДА выставить счет, даже при отсутствии полных данных.

АЛГОРИТМ РАБОТЫ:

1. АНАЛИЗ ЗАЯВКИ - извлеки из письма:
   - Артикулы товаров и их количество
   - Название организации клиента
   - Контактные данные (ФИО, телефон, email)
   - Адрес доставки

2. ПОИСК РЕКВИЗИТОВ ОРГАНИЗАЦИИ:
   - ОБЯЗАТЕЛЬНО используй "findOrgTool" если есть название организации или ИНН
   - Если findOrgTool не нашел данные - продолжай работу с имеющимися данными
   - Недостающие реквизиты заполняй значением "неизвестно"

3. ПОИСК ТОВАРОВ:
   ПРИОРИТЕТ: Используй "smartSearchTool" для автоматического умного поиска

   smartSearchTool автоматически:
   - Определяет тип запроса (артикул, размеры, производитель)
   - Применяет правильную стратегию поиска
   - Делает fallback поиск при отсутствии результатов
   - Фильтрует товары в наличии

   Для каждого товара из заявки вызывай:
   {"query": "описание_товара_или_артикул", "quantity": количество}

   FALLBACK: Если smartSearchTool не находит товары, используй обычный searchTool:
   {"queries": [{"q": "поисковый_запрос", "filters": "опциональные_фильтры"}]}

4. ОБРАБОТКА ДАННЫХ:
   - Нормализация синонимов: манжета→сальник, уплотнение→сальник, рк→ремкомплект
   - Транслитерация брендов: снф→SNF, тто→TTO, бош→Bosch, кортеко→Corteco
   - Размеры в формате '50*80*7' с допуском ±0.3мм
   - Если в запросе есть артикулы, ищи сразу по ним без фильтров!

ПРИМЕРЫ ПРАВИЛЬНОЙ ФИЛЬТРАЦИИ "searchTool":

Внимание! Если указан размер, ВСЕГДА добавляй его в фильтр с допуском +- 0.3

Запрос: "Подшипник рулевой рейки радиальный PSBR05089"
Поиск:
{
  "q": "подшипник PSBR05089"
}

Запрос: "Манжета арм. 188х218х13"
Поиск:
{
  "q": "сальник",
  "filters": "size_in >= 187.7 AND size_in <= 188.3 AND size_out >= 217.7 AND size_out <= 218.3 AND size_h >= 12.7 AND size_h <= 13.3"
}

Запрос: "сальник, манжета P02121"
Поиск:
{
  "q": "сальник P02121"
}

Запрос: "сальник 50 на 80 на 7 SNF"
Поиск 1 (точный):
{
  "q": "сальник",
  "filters": "prod_manuf = 'SNF' AND size_in >= 49.7 AND size_in <= 50.3 AND size_out >= 79.7 AND size_out <= 80.3 AND size_h >= 6.7 AND size_h <= 7.3"
}

Поиск 2 (упрощенный):
{
  "q": "сальник 50*80*7 SNF",
  "filters": "size_in >= 49.7 AND size_in <= 50.3 AND size_out >= 79.7 AND size_out <= 80.3"
}

Запрос: "сальник 10х19 тип TCN1 фирмы TTO"
Поиск 1:
{
  "q": "сальник 10*19 TCN1 TTO",
  "filters": "prod_manuf = 'TTO' AND prod_type = 'TCN1' AND size_in >= 9.7 AND size_in <= 10.3 AND size_out >= 18.7 AND size_out <= 19.3"
}

СЕПАРАЦИЯ СЛОЖНЫХ РАЗМЕРОВ:
"14*30.5/38.4*5/11" →
- size_in = 14 (±0.3)
- size_out = 30.5 (±0.3) 
- size_out_2 = 38.4 (±0.3)
- size_h = 5 (±3)
- size_h_2 = 11 (±3)

P.S "searchTool" работает на MeiliSearch.

5. ГЕНЕРАЦИЯ СЧЕТА - ОБЯЗАТЕЛЬНЫЙ ЭТАП:
   КРИТИЧНО! ВСЕГДА используй generateDocTool для создания счета, даже если:
   - Не все товары найдены
   - Отсутствуют полные реквизиты клиента
   - Нет точных данных организации

   ПРАВИЛА ГЕНЕРАЦИИ:
   - Товары в наличии (prod_count > 0) - добавляй в счет
   - Товары отсутствуют (prod_count = 0) - указывай "временно отсутствует"
   - Недостающие данные клиента - заполняй "неизвестно"
   - Работай автономно, не задавай лишних вопросов

6. ФИНАЛЬНЫЙ ОТВЕТ:
   - Краткое сообщение о клиенту
   - Ссылки на файлы счета (xlsx и pdf) из generateDocTool
   - Ссылки на товары: https://mirsalnikov.ru/catalog/product/{prod_id}

`;

const openai = createOpenAI({
  baseURL: "https://openrouter.ai/api/v1",
  apiKey: process.env.OPENROUTER_API_KEY,
});

const dblink = `file:${process.env.MASTRA_DB_DIR || process.cwd()}/mastra_orgagent.db`;

console.log("🚀 ~ dblink:", dblink)
export const orgAgent = new Agent({
  name: "Org manager Agent",
  instructions,

  // model: openrouter.chat(""),
  model: openai.chat("deepseek/deepseek-chat-v3-0324:free"), //openrouter/cypher-alpha:free
  tools: {
    searchTool,
    smartSearchTool,
    generateDocTool,
    findOrgTool,
  },

  memory: new Memory({
    storage: new LibSQLStore({
      url: dblink,
    }),
    options: {
      lastMessages: 10,
      semanticRecall: false,
      threads: {
        // generateTitle: true,
      },
    },
  }),
});
// orgAgent.generate("Привет").then((res) => console.log(res.text));
