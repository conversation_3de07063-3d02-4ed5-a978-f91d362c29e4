import { createTool } from "@mastra/core/tools";
import { z } from "zod";

// Импортируем существующие схемы из searchTool
import { ApiResponseSchema, type ApiResponse } from "./searchTool";

const SmartSearchInputSchema = z.object({
  query: z.string().describe("Поисковый запрос (артикул, название товара, описание)"),
  quantity: z.number().optional().default(1).describe("Количество товара"),
  fallbackSearch: z.boolean().optional().default(true).describe("Использовать fallback поиск при отсутствии результатов"),
});

export type SmartSearchInput = z.infer<typeof SmartSearchInputSchema>;

const SmartSearchResponseSchema = z.object({
  success: z.boolean(),
  searchAttempts: z.number(),
  foundProducts: z.array(z.object({
    prod_id: z.number(),
    prod_sku: z.string(),
    prod_analogsku: z.string(),
    prod_price: z.string(),
    prod_manuf: z.string(),
    prod_type: z.string(),
    prod_size: z.string(),
    prod_purpose: z.string(),
    prod_count: z.number(),
    quantity: z.number(),
  })),
  message: z.string(),
});

export type SmartSearchResponse = z.infer<typeof SmartSearchResponseSchema>;

export const smartSearchTool = createTool({
  id: "smart_product_search",
  inputSchema: SmartSearchInputSchema,
  outputSchema: SmartSearchResponseSchema,
  description: `Умный поиск товаров с автоматическими fallback стратегиями. Автоматически пробует разные варианты поиска до получения результата.`,
  execute: async ({ context }) => {
    const result = await smartSearch(context);
    return result as SmartSearchResponse;
  },
});

async function smartSearch(input: SmartSearchInput): Promise<SmartSearchResponse> {
  const { query, quantity = 1, fallbackSearch = true } = input;
  let searchAttempts = 0;
  let foundProducts: any[] = [];

  console.log("🔍 Начинаем умный поиск для:", query);

  // Стратегии поиска в порядке приоритета
  const searchStrategies = [
    // 1. Точный поиск по артикулу (если похоже на артикул)
    () => isArticleCode(query) ? searchByArticle(query) : null,
    
    // 2. Поиск с размерами (если есть размеры в запросе)
    () => hasSize(query) ? searchWithSizeFilters(query) : null,
    
    // 3. Поиск с производителем
    () => hasManufacturer(query) ? searchWithManufacturer(query) : null,
    
    // 4. Базовый поиск по ключевым словам
    () => searchBasic(query),
    
    // 5. Широкий поиск (упрощенные термины)
    () => searchWide(query),
    
    // 6. Поиск по синонимам
    () => searchBySynonyms(query),
  ];

  for (const strategy of searchStrategies) {
    if (!fallbackSearch && searchAttempts > 0) break;
    
    const searchQuery = strategy();
    if (!searchQuery) continue;

    searchAttempts++;
    console.log(`🔍 Попытка ${searchAttempts}:`, JSON.stringify(searchQuery));

    try {
      const result = await executeSearch(searchQuery);
      if (result?.result?.data && result.result.data.length > 0) {
        // Фильтруем товары в наличии
        const availableProducts = result.result.data.filter(product => product.prod_count > 0);
        
        if (availableProducts.length > 0) {
          foundProducts = availableProducts.map(product => ({
            ...product,
            quantity: quantity,
          }));
          
          console.log(`✅ Найдено ${foundProducts.length} товаров в наличии`);
          break;
        }
      }
    } catch (error) {
      console.error(`❌ Ошибка в попытке ${searchAttempts}:`, error);
    }
  }

  return {
    success: foundProducts.length > 0,
    searchAttempts,
    foundProducts,
    message: foundProducts.length > 0 
      ? `Найдено ${foundProducts.length} товаров за ${searchAttempts} попыток`
      : `Товары не найдены после ${searchAttempts} попыток поиска`,
  };
}

// Вспомогательные функции для определения типа запроса
function isArticleCode(query: string): boolean {
  // Артикул обычно содержит буквы и цифры, может содержать дефисы
  return /^[A-Za-z0-9\-_]{3,}$/.test(query.trim());
}

function hasSize(query: string): boolean {
  // Проверяем наличие размеров типа "50x80x7" или "50*80*7"
  return /\d+[x*×]\d+[x*×]?\d*/.test(query);
}

function hasManufacturer(query: string): boolean {
  const manufacturers = ['SNF', 'TTO', 'Bosch', 'Corteco', 'TCS', 'Volkswagen', 'MAN'];
  return manufacturers.some(brand => query.toLowerCase().includes(brand.toLowerCase()));
}

// Стратегии поиска
function searchByArticle(query: string) {
  const cleanQuery = query.trim().replace(/\s+/g, '');
  return {
    queries: [{ q: cleanQuery }]
  };
}

function searchWithSizeFilters(query: string) {
  const sizes = extractSizes(query);
  const cleanQuery = query.replace(/\d+[x*×]\d+[x*×]?\d*/g, '').trim();
  
  if (sizes.length >= 2) {
    const filters = `size_in >= ${sizes[0] - 0.3} AND size_in <= ${sizes[0] + 0.3} AND size_out >= ${sizes[1] - 0.3} AND size_out <= ${sizes[1] + 0.3}`;
    return {
      queries: [{ q: cleanQuery, filters }]
    };
  }
  
  return { queries: [{ q: query }] };
}

function searchWithManufacturer(query: string) {
  const manufacturer = extractManufacturer(query);
  if (manufacturer) {
    return {
      queries: [{ q: query, filters: `prod_manuf = '${manufacturer}'` }]
    };
  }
  return { queries: [{ q: query }] };
}

function searchBasic(query: string) {
  return { queries: [{ q: normalizeQuery(query) }] };
}

function searchWide(query: string) {
  const keywords = extractKeywords(query);
  return { queries: [{ q: keywords.join(' ') }] };
}

function searchBySynonyms(query: string) {
  const synonymQuery = applySynonyms(query);
  return { queries: [{ q: synonymQuery }] };
}

// Вспомогательные функции обработки
function extractSizes(query: string): number[] {
  const sizeMatch = query.match(/(\d+)[x*×](\d+)(?:[x*×](\d+))?/);
  if (sizeMatch) {
    return [
      parseInt(sizeMatch[1]),
      parseInt(sizeMatch[2]),
      sizeMatch[3] ? parseInt(sizeMatch[3]) : 0
    ].filter(size => size > 0);
  }
  return [];
}

function extractManufacturer(query: string): string | null {
  const manufacturers = {
    'снф': 'SNF', 'тто': 'TTO', 'бош': 'Bosch', 
    'кортеко': 'Corteco', 'тцс': 'TCS'
  };
  
  for (const [rus, eng] of Object.entries(manufacturers)) {
    if (query.toLowerCase().includes(rus)) {
      return eng;
    }
  }
  return null;
}

function normalizeQuery(query: string): string {
  return query
    .toLowerCase()
    .replace(/манжет[аы]?/g, 'сальник')
    .replace(/уплотнени[ея]/g, 'сальник')
    .replace(/р[\/]?к/g, 'ремкомплект')
    .trim();
}

function extractKeywords(query: string): string[] {
  return query
    .split(/\s+/)
    .filter(word => word.length > 2)
    .slice(0, 3); // Берем первые 3 ключевых слова
}

function applySynonyms(query: string): string {
  const synonyms = {
    'манжета': 'сальник',
    'уплотнение': 'сальник',
    'рк': 'ремкомплект',
    'р/к': 'ремкомплект',
  };
  
  let result = query.toLowerCase();
  for (const [original, synonym] of Object.entries(synonyms)) {
    result = result.replace(new RegExp(original, 'g'), synonym);
  }
  return result;
}

// Функция выполнения поиска (использует существующий API)
async function executeSearch(searchQuery: any): Promise<ApiResponse | null> {
  const apiURL = process.env.SEARCH_API_URL;
  if (!apiURL) {
    throw new Error("SEARCH_API_URL не настроен");
  }

  const input = JSON.stringify(searchQuery);
  const url = `${apiURL}?input=${input}`;

  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error("❌ Ошибка при выполнении поиска:", error);
    return null;
  }
}
