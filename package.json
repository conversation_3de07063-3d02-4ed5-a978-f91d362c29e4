{"name": "rti-ai", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@mastra/core": "^0.10.10", "@mastra/libsql": "^0.10.3", "@mastra/memory": "^0.10.4", "@openrouter/ai-sdk-provider": "^0.7.2", "openai": "^5.8.2", "zod": "^3.25.75"}, "devDependencies": {"@types/node": "^22.16.0", "mastra": "^0.10.10", "typescript": "^5.8.3"}}