import { createTool } from "@mastra/core/tools";
import { z } from "zod";

// Схема для входных данных - универсальное значение для поиска (название или ИНН)
export const FindOrgInputSchema = z.object({
  query: z.string().describe("Название организации или ИНН для поиска реквизитов"),
});
export type FindOrgInput = z.infer<typeof FindOrgInputSchema>;

// Схема для ответа API с реквизитами организации
export const OrgDetailsSchema = z.object({
  name: z.string().optional().describe("Полное наименование организации"),
  inn: z.string().optional().describe("ИНН организации"),
  kpp: z.string().optional().describe("КПП организации"),
  ogrn: z.string().optional().describe("ОГРН организации"),
  address: z.string().optional().describe("Юридический адрес организации"),
  director: z.string().optional().describe("Руководитель организации"),
  phone: z.string().optional().describe("Телефон организации"),
  email: z.string().optional().describe("Email организации"),
  website: z.string().optional().describe("Веб-сайт организации"),
  okved: z.string().optional().describe("Основной ОКВЭД"),
  status: z.string().optional().describe("Статус организации"),
  registrationDate: z.string().optional().describe("Дата регистрации"),
  capital: z.string().optional().describe("Уставный капитал"),
});
export type OrgDetails = z.infer<typeof OrgDetailsSchema>;

// Схема для полного ответа API
export const FindOrgResponseSchema = z.object({
  success: z.boolean().describe("Успешность запроса"),
  data: OrgDetailsSchema.optional().describe("Данные организации"),
  message: z.string().optional().describe("Сообщение об ошибке или статусе"),
});
export type FindOrgResponse = z.infer<typeof FindOrgResponseSchema>;

export const findOrgTool = createTool({
  id: "find_organization_details",
  inputSchema: FindOrgInputSchema,
  outputSchema: FindOrgResponseSchema,
  description: `Поиск всех реквизитов организации по названию или ИНН. Возвращает ИНН, КПП, ОГРН, адрес, руководителя и другую информацию об организации.`,
  execute: async ({ context }) => {
    const result = await findOrganization(context.query);
    return result as FindOrgResponse;
  },
});

// queryValue может быть названием или ИНН
async function findOrganization(queryValue: string): Promise<FindOrgResponse> {
  try {
    // Кодируем значение для URL
    const encodedValue = encodeURIComponent(queryValue);
    const apiUrl = `https://mirsalnikov.ru/api/service/findorg/${encodedValue}`;

    console.log("🔍 Поиск организации по значению:", queryValue);
    console.log("📡 URL запроса:", apiUrl);

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "accept": "*/*",
        "accept-language": "ru-RU,ru;q=0.9,en-GB;q=0.8,en;q=0.7,en-US;q=0.6,es;q=0.5",
        "content-type": "application/json",
        "priority": "u=1, i",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Linux"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        // Убираем cookie для безопасности - они могут быть специфичными для сессии
      },
    });

    console.log("📡 Статус ответа:", response.status);

    if (!response.ok) {
      return {
        success: false,
        message: `Ошибка HTTP: ${response.status} ${response.statusText}`,
      };
    }

    const data = await response.json();
    console.log("🚀 ~ findOrganization ~ data:", data);

    // Обрабатываем ответ в зависимости от структуры API
    // Предполагаем, что API возвращает объект с информацией об организации
    if (data && typeof data === 'object') {
      return {
        success: true,
        data: {
          name: data.name || data.fullName || data.orgName,
          inn: data.inn,
          kpp: data.kpp,
          ogrn: data.ogrn,
          address: data.address || data.legalAddress || data.addr,
          director: data.director || data.ceo || data.head,
          phone: data.phone || data.telephone,
          email: data.email || data.mail,
          website: data.website || data.site,
          okved: data.okved || data.mainOkved,
          status: data.status || data.state,
          registrationDate: data.registrationDate || data.regDate,
          capital: data.capital || data.authorizedCapital,
        },
        message: "Данные организации успешно получены",
      };
    } else {
      return {
        success: false,
        message: "Организация не найдена или получен некорректный ответ от API",
      };
    }

  } catch (error) {
    console.error("❌ Ошибка при поиске организации:", error);
    return {
      success: false,
      message: `Ошибка при выполнении запроса: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`,
    };
  }
}
