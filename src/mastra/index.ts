import { <PERSON><PERSON> } from "@mastra/core";
import { sealsAgent } from "./agents/sealsAgent";
import { orgAgent } from "./agents/orgAgent";
import { LibSQLStore } from "@mastra/libsql";
import { ConsoleLogger } from "@mastra/core/logger";

export const mastra = new Mastra({
  agents: { sealsAgent, orgAgent },
  storage: new LibSQLStore({
    url: ":memory:",
  }),
  logger: new ConsoleLogger({
    level: "info",
    name: "mastra",
  }),
});
